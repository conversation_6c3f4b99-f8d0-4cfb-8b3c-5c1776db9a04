package middlewares

import (
	"net/http"

	"github.com/SneatX/adeptos_go/internal/config"
	"github.com/go-chi/cors"
)

func NewCORS(env *config.EnvConfig) func(http.Handler) http.Handler {
	corsOptions := cors.Options{
		AllowedOrigins:   []string{env.ClientConfig.Origin},
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"Accept", "Authorization", "Content-Type", "X-CSRF-Token"},
		ExposedHeaders:   []string{"Link"},
		AllowCredentials: true,
		MaxAge:           300,
	}
	return cors.New(corsOptions).Handler
}
