package handlers

import (
	"net/http"
	"strconv"

	"github.com/SneatX/adeptos_go/internal/adapters/http/responses"
	"github.com/SneatX/adeptos_go/internal/ports"
	"github.com/go-chi/chi/v5"
)

type ReportHandler struct {
	reportService ports.ReportService
}

func NewReportHandler(reportService ports.ReportService) *ReportHandler {
	return &ReportHandler{reportService: reportService}
}

func (h *ReportHandler) GetReportByID(w http.ResponseWriter, r *http.Request) {
	idStr := chi.URLParam(r, "id")
	if idStr == "" {
		responses.ErrorResponse(w, http.StatusBadRequest, "Missing report ID")
		return
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		responses.ErrorResponse(w, http.StatusBadRequest, "Invalid report ID")
		return
	}

	report, err := h.reportService.GetReportByID(uint(id))
	if err != nil {
		responses.ErrorResponse(w, http.StatusNotFound, err.Error())
		return
	}

	responses.SuccessOne(w, http.StatusOK, report)
}
