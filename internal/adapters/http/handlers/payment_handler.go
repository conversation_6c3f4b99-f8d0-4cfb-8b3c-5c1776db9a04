package handlers

import (
	"net/http"

	"github.com/SneatX/adeptos_go/internal/adapters/http/responses"
	"github.com/SneatX/adeptos_go/internal/adapters/stripe"
)

type PaymentHandler struct {
	stripeService *stripe.StripeService
}

func NewPaymentHandler(stripeService *stripe.StripeService) *PaymentHandler {
	return &PaymentHandler{stripeService: stripeService}
}

func (h *PaymentHandler) CreateCheckoutSession(w http.ResponseWriter, r *http.Request) {
	url, err := h.stripeService.CreateCheckoutSession()
	if err != nil {
		responses.ErrorResponse(w, http.StatusInternalServerError, err.Error())
		return
	}

	responses.SuccessOne(w, http.StatusOK, map[string]string{
		"url": url,
	})
}
