package stripe

import (
	"fmt"

	"github.com/stripe/stripe-go/v82"
	"github.com/stripe/stripe-go/v82/checkout/session"
)

type StripeService struct {
	apiKey     string
	priceID    string
	successURL string
	cancelURL  string
}

func NewStripeService(apiKey string, priceID string, successURL string, cancelURL string) *StripeService {
	stripe.Key = apiKey
	return &StripeService{
		apiKey:     apiKey,
		priceID:    priceID,
		successURL: successURL,
		cancelURL:  cancelURL,
	}
}

func (s *StripeService) CreateCheckoutSession() (string, error) {
	params := &stripe.CheckoutSessionParams{
		LineItems: []*stripe.CheckoutSessionLineItemParams{
			{
				Price:    stripe.String(s.priceID),
				Quantity: stripe.Int64(1),
			},
		},
		Mode:       stripe.String(string(stripe.CheckoutSessionModePayment)),
		SuccessURL: stripe.String(s.successURL),
		CancelURL:  stripe.String(s.cancelURL),
	}

	sess, err := session.New(params)
	fmt.Println(sess.URL)
	if err != nil {
		return "", err
	}

	return sess.URL, nil
}
