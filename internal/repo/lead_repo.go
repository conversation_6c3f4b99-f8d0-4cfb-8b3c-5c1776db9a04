package repo

import (
	"github.com/SneatX/adeptos_go/internal/domain"
	"gorm.io/gorm"
)

type LeadRepo struct {
	DB *gorm.DB
}

func NewLeadRepo(db *gorm.DB) *LeadRepo {
	return &LeadRepo{DB: db}
}

func (r *LeadRepo) GetByID(id uint) (*domain.Lead, error) {
	var lead domain.Lead
	result := r.DB.Preload("Reports").First(&lead, id)
	if result.Error != nil {
		return nil, result.Error
	}
	return &lead, nil
}

func (r *LeadRepo) Create(lead *domain.Lead) (*domain.Lead, error) {
	result := r.DB.Create(lead)
	if result.Error != nil {
		return nil, result.Error
	}
	return lead, nil
}
