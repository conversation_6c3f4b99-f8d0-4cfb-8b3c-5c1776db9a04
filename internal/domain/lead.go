package domain

import "time"

type Lead struct {
	ID                 uint      `gorm:"primaryKey" json:"id"`
	FullName           string    `json:"fullName"`
	CompanyName        string    `json:"companyName"`
	Email              string    `json:"email"`
	Phone              string    `json:"phone"`
	Website            *string   `json:"website"`
	BusinessType       string    `json:"businessType"`
	EmployeeCount      string    `json:"employeeCount"`
	Industry           string    `json:"industry"`
	MonthlyRevenue     string    `json:"monthlyRevenue"`
	SalesLocation      string    `json:"salesLocation"`
	Bottlenecks        string    `json:"bottlenecks"`
	TimeConsumingTasks string    `json:"timeConsumingTasks"`
	Reports            []Report  `gorm:"foreignKey:LeadID" json:"reports,omitempty"`
	CreatedAt          time.Time `json:"createdAt"`
	UpdatedAt          time.Time `json:"updatedAt"`
}
