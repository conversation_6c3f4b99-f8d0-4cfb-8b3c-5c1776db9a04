package config

import (
	"github.com/SneatX/adeptos_go/internal/adapters/http/handlers"
	"github.com/SneatX/adeptos_go/internal/adapters/openai"
	"github.com/SneatX/adeptos_go/internal/adapters/stripe"
	"github.com/SneatX/adeptos_go/internal/repo"
	"github.com/SneatX/adeptos_go/internal/service"
	"gorm.io/gorm"
)

type Handlers struct {
	LeadHandler    *handlers.LeadHandler
	ReportHandler  *handlers.ReportHandler
	PaymentHandler *handlers.PaymentHandler
}

type Services struct {
	LeadService   *service.LeadService
	ReportService *service.ReportService
}

type Adapters struct {
	OpenAIService *openai.OpenAIService
	StripeService *stripe.StripeService
}

type Repositories struct {
	LeadRepo   *repo.LeadRepo
	ReportRepo *repo.ReportRepo
}

type Container struct {
	DB           *gorm.DB
	Repositories *Repositories
	Services     *Services
	Adapters     *Adapters
	Handlers     *Handlers
}

func BuildContainer(env *EnvConfig) *Container {
	DB := InitDatabase(&env.DBConfig)

	// Initialize repositories
	leadRepo := repo.NewLeadRepo(DB)
	reportRepo := repo.NewReportRepo(DB)

	// Initialize services
	gptService := openai.NewOpenAIService(env.OPENAIConfig.APIKey)
	stripeService := stripe.NewStripeService(env.StripeConfig.APIKey, env.StripeConfig.PriceID, env.StripeConfig.SuccessURL, env.StripeConfig.CancelURL)
	reportService := service.NewReportService(reportRepo)
	leadService := service.NewLeadService(leadRepo, gptService, reportService)

	// Initialize handlers
	leadHandler := handlers.NewLeadHandler(leadService)
	reportHandler := handlers.NewReportHandler(reportService)
	paymentHandler := handlers.NewPaymentHandler(stripeService)

	return &Container{
		DB: DB,
		Repositories: &Repositories{
			LeadRepo:   leadRepo,
			ReportRepo: reportRepo,
		},
		Services: &Services{
			LeadService:   leadService,
			ReportService: reportService,
		},
		Adapters: &Adapters{
			OpenAIService: gptService,
			StripeService: stripeService,
		},
		Handlers: &Handlers{
			LeadHandler:    leadHandler,
			ReportHandler:  reportHandler,
			PaymentHandler: paymentHandler,
		},
	}
}
