package service

import (
	"github.com/SneatX/adeptos_go/internal/domain"
	"github.com/SneatX/adeptos_go/internal/ports"
)

type LeadService struct {
	leadRepo      ports.LeanRepository
	gptService    ports.GPTService
	reportService ports.ReportService
}

func NewLeadService(leadRepo ports.LeanRepository, gptService ports.GPTService, reportService ports.ReportService) *LeadService {
	return &LeadService{
		leadRepo:      leadRepo,
		gptService:    gptService,
		reportService: reportService,
	}
}

func (s *LeadService) GetLeadByID(id uint) (*domain.Lead, error) {
	return s.leadRepo.GetByID(id)
}

func (s *LeadService) CreateLeadAndGenerateResponse(lead *domain.Lead) (*ports.CreateLeadOutput, error) {
	createdLead, err := s.leadRepo.Create(lead)
	if err != nil {
		return nil, err
	}

	input := ports.LeadInput{
		FullName:           createdLead.FullName,
		CompanyName:        createdLead.CompanyName,
		Email:              createdLead.Email,
		Phone:              createdLead.Phone,
		Website:            createdLead.Website,
		Industry:           createdLead.Industry,
		EmployeeCount:      createdLead.EmployeeCount,
		BusinessType:       createdLead.BusinessType,
		MonthlyRevenue:     createdLead.MonthlyRevenue,
		SalesLocation:      createdLead.SalesLocation,
		TimeConsumingTasks: createdLead.TimeConsumingTasks,
		Bottlenecks:        createdLead.Bottlenecks,
	}

	gptResponse, err := s.gptService.GenerateResponse(input)
	if err != nil {
		return nil, err
	}

	savedReport, err := s.reportService.CreateReport(createdLead.ID, gptResponse.Data)
	if err != nil {
		return nil, err
	}

	return &ports.CreateLeadOutput{
		Lead:        createdLead,
		GPTResponse: savedReport,
	}, nil
}
